<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stockart_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        // Database will be loaded on demand
        $this->load->library('external_api');
    }

    /**
     * Build base query for patient data
     *
     * @return void
     */
    private function _build_patient_query() {
        $this->db->select('
            pk.NOMOR AS nokun,
            mp.NAMA AS nama_pasien,
            mp.NORM AS norm,
            mp.JENIS_KELAMIN AS jenis_kelamin,
            mp.TANGGAL_LAHIR as tanggal_lahir,
            md.id as id_dokter,
            master.getNamaLengkapPegawai(md.NIP) AS nama_dokter,
            mrkt.TEMPAT_TIDUR as kasur,
            mrk.KAMAR as kamar,
            pk.MASUK as tanggal_kunjungan,
            pk.ref
        ');

        $this->db->from('pendaftaran.kunjungan pk');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN');
        $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM');
        $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pp.NOMOR');
        $this->db->join('master.dokter md', 'md.ID = ptp.DOKTER');
        $this->db->join('master.ruang_kamar_tidur mrkt', 'pk.RUANG_KAMAR_TIDUR = mrkt.ID');
        $this->db->join('master.ruang_kamar mrk', 'mrk.ID = mrkt.RUANG_KAMAR');
    }

    /**
     * Get patient data by nokun (visit number)
     * 
     * @param string $nokun
     * @return array|null
     */
    public function get_patient_data_by_nokun($nokun) {
        // Load database if not already loaded
        if (!isset($this->db)) {
            $this->load->database();
        }

        try {
            $this->_build_patient_query();
            $this->db->where('pk.NOMOR', $nokun);

            $query = $this->db->get();
            return $query->num_rows() > 0 ? $query->row_array() : null;
        } catch (Exception $e) {
            log_message('error', 'Error fetching patient data by nokun: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get patient data by ref (reference number)
     * 
     * @param string $ref
     * @return array|null
     */
    public function get_patient_data_by_ref($ref) {
        // Load database if not already loaded
        if (!isset($this->db)) {
            $this->load->database();
        }

        try {
            $this->_build_patient_query();
            $this->db->where('pk.REF', $ref);

            $query = $this->db->get();
            return $query->num_rows() > 0 ? $query->row_array() : null;
        } catch (Exception $e) {
            log_message('error', 'Error fetching patient data by ref: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Convert patient data to external API format based on EventType
     * 
     * @param array $data
     * @param string $event_type
     * @param array $additional_params
     * @return array
     */
    public function convert_to_api_format($data, $event_type = 'AdmitPatient', $additional_params = array()) {
        // Split patient name into first name and last name
        $nama_parts = explode(' ', trim($data['nama_pasien']));
        $first_name = isset($nama_parts[0]) ? $nama_parts[0] : '';
        $last_name = count($nama_parts) > 1 ? implode(' ', array_slice($nama_parts, 1)) : '';

        // Convert gender (1 = Male = M, 2 = Female = F)
        $gender = ($data['jenis_kelamin'] == 1) ? 'M' : 'F';

        // Convert birth date to YYYYMMDD format
        $birth_date = date('Ymd', strtotime($data['tanggal_lahir']));

        // Determine NurseUnit based on EventType
        $nurse_unit = 'ICU'; // Default value
        if ($event_type === 'TransferPatient' && isset($additional_params['nurseunit'])) {
            $nurse_unit = $additional_params['nurseunit'];
        }

        // Base data used for all event types
        $base_data = array(
            'EventType' => $event_type,
            'PatientID' => $data['norm'], // PatientID = norm (medical record number)
            'PatientLastName' => $last_name,
            'PatientFirstName' => $first_name,
            'NurseUnit' => $nurse_unit,
            'EpisodeID' => $data['nokun'], // EpisodeID = nokun (visit number)
        );

        // Add specific fields based on EventType
        switch ($event_type) {
            case 'AdmitPatient':
                // Convert visit date to YYYYMMDDHHMM format
                $admission_date = date('YmdHi', strtotime($data['tanggal_kunjungan']));
                
                $base_data['BirthDate'] = $birth_date;
                $base_data['Gender'] = $gender;
                $base_data['Room'] = $data['kamar'];
                $base_data['Bed'] = $data['kasur'];
                $base_data['AttendingDocID'] = (string)$data['id_dokter'];
                $base_data['AttendingDocName'] = $data['nama_dokter'];
                $base_data['AdmissionDate'] = $admission_date;
                break;

            case 'DischargePatient':
                // Convert visit date to YYYYMMDDHHMM format for DischargeDate
                $discharge_date = date('YmdHi', strtotime($data['tanggal_kunjungan']));
                $base_data['DischargeDate'] = $discharge_date;
                break;

            case 'UpdatePatient':
                // UpdatePatient uses complete data from nokun query like AdmitPatient
                $base_data['BirthDate'] = $birth_date;
                $base_data['Gender'] = $gender;
                break;

            case 'TransferPatient':
                // TransferPatient only uses basic data + nurseunit from parameter
                $base_data['BirthDate'] = $birth_date;
                $base_data['Gender'] = $gender;
                break;

            default:
                // Fallback for unknown EventType
                $base_data['BirthDate'] = $birth_date;
                $base_data['Gender'] = $gender;
                break;
        }

        return $base_data;
    }

    /**
     * Process patient data with EventType: get data, convert based on EventType, and send to external API
     * 
     * @param string $search_param
     * @param string $event_type
     * @param array $additional_params
     * @param string $search_type
     * @return array
     */
    public function process_patient_data_with_event_type($search_param, $event_type, $additional_params = array(), $search_type = 'nokun') {
        try {
            // Get patient data based on search type
            if ($search_type === 'ref') {
                $patient_data = $this->get_patient_data_by_ref($search_param);
            } else {
                $patient_data = $this->get_patient_data_by_nokun($search_param);
            }

            if (!$patient_data) {
                return array(
                    'success' => false,
                    'message' => "Data tidak ditemukan untuk {$search_type}: {$search_param}",
                    'data' => null
                );
            }

            // Convert to API format based on EventType
            $api_data = $this->convert_to_api_format($patient_data, $event_type, $additional_params);

            // Send to external API
            $api_response = $this->external_api->send_to_external_api($api_data);

            return array(
                'success' => true,
                'message' => $api_response['success'] ?
                    "Data berhasil dikirim ke API eksternal dengan EventType: {$event_type}" :
                    'Data berhasil diproses, namun API eksternal tidak dapat diakses',
                'data' => array(
                    'patient_data' => $patient_data,
                    'api_payload' => $api_data,
                    'api_response' => $api_response,
                    'api_success' => $api_response['success'],
                    'event_type' => $event_type,
                    'additional_params' => $additional_params,
                    'search_type' => $search_type,
                    'search_param' => $search_param
                )
            );

        } catch (Exception $e) {
            log_message('error', 'Error processing patient data with event type: ' . $e->getMessage());
            throw $e;
        }
    }


}
