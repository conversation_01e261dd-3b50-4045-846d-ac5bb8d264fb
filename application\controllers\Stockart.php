<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stockart extends CI_Controller {

    public function __construct() {
        parent::__construct();

        // Set timezone to Asia/Jakarta
        date_default_timezone_set('Asia/Jakarta');

        // Set JSON response headers
        $this->output->set_content_type('application/json');

        // Initialize API logging
        $this->load->library('api_logger');
        $this->api_logger->start_logging();

        // Log the request (basic logging - detailed logging handled by api_logger)
        log_message('info', date('Y-m-d H:i:s') . ' - ' . $this->input->method() . ' ' . $this->uri->uri_string());
    }

    /**
     * Default API information endpoint
     */
    public function index() {
        $response = array(
            'success' => true,
            'message' => 'Stockart API - CodeIgniter 3 Version with API Logging',
            'version' => '1.1.0',
            'server' => $_SERVER['SERVER_ADDR'] ?? 'localhost',
            'features' => array(
                'API Request/Response Logging',
                'Patient Data Processing',
                'External API Integration',
                'Comprehensive Error Handling'
            ),
            'endpoints' => array(
                'POST /api/v1/stockart/patient' => 'Process patient data with EventType',
                'GET /api/health' => 'Health check',
                'GET /api/logs' => 'View API request logs (with optional filters)'
            ),
            'logging_info' => array(
                'description' => 'All API requests are automatically logged to database',
                'table' => 'log.log_api',
                'captured_data' => array(
                    'HTTP Method',
                    'Request URL',
                    'Request Headers',
                    'Request Body',
                    'Response Body',
                    'Status Code',
                    'Client IP Address',
                    'Timestamp'
                )
            )
        );

        $this->output->set_output(json_encode($response));

        // End API logging
        $this->_end_api_logging($response, 200);
    }

    /**
     * Handle patient API endpoint
     */
    public function patient() {
        try {
            // Load required libraries and models
            $this->load->model('stockart_model');
            $this->load->library('external_api');

            // Only allow POST method
            if ($this->input->method() !== 'post') {
                $this->_send_error_response('Method not allowed', 405);
                return;
            }

            // Get JSON input from request body
            $json_input = json_decode($this->input->raw_input_stream, true);

            // Debug: Log the received JSON input
            log_message('info', 'Raw input stream: ' . $this->input->raw_input_stream);
            log_message('info', 'Parsed JSON input: ' . json_encode($json_input));

            // Get parameters from JSON body, POST form data, or query string
            $nokun = null;
            $ref = null;

            if ($json_input && is_array($json_input)) {
                $nokun = $json_input['nokun'] ?? null;
                $ref = $json_input['ref'] ?? null;
            }

            // Fallback to form data or query string if JSON is empty
            if (!$nokun && !$ref) {
                $nokun = $this->input->post('nokun') ?: $this->input->get('nokun');
                $ref = $this->input->post('ref') ?: $this->input->get('ref');
            }

            // Validate that either nokun or ref is provided (but not both)
            if (!$nokun && !$ref) {
                $this->_send_error_response('Parameter nokun atau ref wajib diisi (pilih salah satu)', 400);
                return;
            }

            if ($nokun && $ref) {
                $this->_send_error_response('Tidak boleh mengisi nokun dan ref bersamaan. Pilih salah satu saja', 400);
                return;
            }

            // Get eventType from JSON or form data
            $event_type = null;
            if ($json_input && is_array($json_input)) {
                $event_type = $json_input['eventType'] ?? null;
            }

            // Fallback to form data or query string
            if (!$event_type) {
                $event_type = $this->input->post('eventType') ?: $this->input->get('eventType');
            }

            // Validate EventType parameter is required
            if (!$event_type) {
                $this->_send_error_response('Parameter eventType wajib diisi', 400);
                return;
            }

            // Validate allowed EventTypes
            $allowed_event_types = array('AdmitPatient', 'DischargePatient', 'TransferPatient', 'UpdatePatient');
            if (!in_array($event_type, $allowed_event_types)) {
                $this->_send_error_response('EventType tidak valid. Harus salah satu dari: ' . implode(', ', $allowed_event_types), 400);
                return;
            }

            // Get nurseunit from JSON or form data
            $nurse_unit = null;
            if ($json_input && is_array($json_input)) {
                $nurse_unit = $json_input['nurseunit'] ?? null;
            }

            // Fallback to form data or query string
            if (!$nurse_unit) {
                $nurse_unit = $this->input->post('nurseunit') ?: $this->input->get('nurseunit');
            }

            // Special validation for TransferPatient
            if ($event_type === 'TransferPatient') {
                if (!$nurse_unit) {
                    $this->_send_error_response('Parameter nurseunit wajib diisi untuk EventType TransferPatient', 400);
                    return;
                }
            }

            // Get additional parameters for transfer
            $additional_params = array();
            if ($event_type === 'TransferPatient') {
                $additional_params['nurseunit'] = $nurse_unit;
            }

            // Determine which parameter to use
            $search_param = $nokun ?: $ref;
            $search_type = $nokun ? 'nokun' : 'ref';

            // Process data using model with EventType
            $result = $this->stockart_model->process_patient_data_with_event_type(
                $search_param, 
                $event_type, 
                $additional_params, 
                $search_type
            );

            if (!$result['success']) {
                $this->_send_error_response($result['message'], 404);
                return;
            }

            $this->output->set_output(json_encode($result));

            // End API logging for successful response
            $this->_end_api_logging($result, 200);

        } catch (Exception $e) {
            log_message('error', 'Error in patient controller: ' . $e->getMessage());
            $this->_send_error_response('Terjadi kesalahan: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Health check endpoint
     */
    public function health_check() {
        try {
            $response = array(
                'success' => true,
                'message' => 'API is running',
                'timestamp' => date('c'), // ISO 8601 format
                'version' => '1.0.0'
            );

            $this->output->set_output(json_encode($response));

            // End API logging
            $this->_end_api_logging($response, 200);
        } catch (Exception $e) {
            log_message('error', 'Error in health_check: ' . $e->getMessage());
            $this->_send_error_response('Health check failed', 500);
        }
    }



    /**
     * API Logs endpoint - view API request logs
     */
    public function api_logs() {
        try {
            // Load the API log model
            $this->load->model('api_log_model');

            // Get parameters
            $limit = (int) ($this->input->get('limit') ?: 50);
            $offset = (int) ($this->input->get('offset') ?: 0);
            $method = $this->input->get('method');
            $status = $this->input->get('status');
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');

            // Validate limit
            if ($limit > 1000) $limit = 1000;
            if ($limit < 1) $limit = 50;

            $logs = array();
            $total_count = 0;

            // Get logs based on filters
            if ($method) {
                $logs = $this->api_log_model->get_logs_by_method($method, $limit, $offset);
            } elseif ($status) {
                $logs = $this->api_log_model->get_logs_by_status((int)$status, $limit, $offset);
            } elseif ($start_date && $end_date) {
                $logs = $this->api_log_model->get_logs_by_date($start_date, $end_date, $limit, $offset);
            } else {
                $logs = $this->api_log_model->get_logs($limit, $offset);
            }

            $total_count = $this->api_log_model->get_logs_count();

            $response = array(
                'success' => true,
                'message' => 'API logs retrieved successfully',
                'data' => array(
                    'logs' => $logs,
                    'pagination' => array(
                        'total' => $total_count,
                        'limit' => $limit,
                        'offset' => $offset,
                        'current_count' => count($logs)
                    )
                ),
                'filters' => array(
                    'method' => $method,
                    'status' => $status,
                    'start_date' => $start_date,
                    'end_date' => $end_date
                )
            );

            $this->output->set_output(json_encode($response));

            // End API logging
            $this->_end_api_logging($response, 200);

        } catch (Exception $e) {
            log_message('error', 'Error in api_logs controller: ' . $e->getMessage());
            $this->_send_error_response('Failed to retrieve API logs: ' . $e->getMessage(), 500);
        }
    }



    /**
     * 404 Not Found handler
     */
    public function not_found() {
        $this->_send_error_response('Endpoint not found', 404);
    }

    /**
     * Send error response
     *
     * @param string $message
     * @param int $status_code
     */
    private function _send_error_response($message, $status_code = 400) {
        $this->output->set_status_header($status_code);

        $response = array(
            'success' => false,
            'message' => $message
        );

        // Add error details in development environment
        if (ENVIRONMENT === 'development' && $status_code === 500) {
            $response['error'] = $message;
        }

        $this->output->set_output(json_encode($response));

        // End API logging for error responses
        $this->_end_api_logging($response, $status_code);
    }

    /**
     * End API logging
     *
     * @param mixed $response_data
     * @param int $status_code
     */
    private function _end_api_logging($response_data, $status_code = 200) {
        try {
            if (isset($this->api_logger)) {
                $this->api_logger->end_logging($response_data, $status_code);
            }
        } catch (Exception $e) {
            log_message('error', 'Error ending API logging: ' . $e->getMessage());
        }
    }
}
