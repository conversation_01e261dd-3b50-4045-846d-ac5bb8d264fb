<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_logger {

    protected $CI;
    private $start_time;
    private $request_data;

    public function __construct() {
        $this->CI =& get_instance();
        $this->start_time = microtime(true);
        $this->request_data = array();
    }

    /**
     * Start logging - capture request data
     */
    public function start_logging() {
        try {
            // Capture request data
            $this->request_data = array(
                'method' => $this->CI->input->method(true),
                'url' => $this->_get_full_url(),
                'headers' => $this->_get_request_headers(),
                'request_body' => $this->_get_request_body(),
                'ip_address' => $this->_get_client_ip(),
                'user_agent' => $this->CI->input->user_agent(),
                'timestamp' => date('Y-m-d H:i:s')
            );

            // Log request start
            log_message('info', 'API Request Started: ' . $this->request_data['method'] . ' ' . $this->request_data['url']);

        } catch (Exception $e) {
            log_message('error', 'Error starting API logging: ' . $e->getMessage());
        }
    }

    /**
     * End logging - capture response and save to database
     * 
     * @param mixed $response_data
     * @param int $status_code
     */
    public function end_logging($response_data = null, $status_code = 200) {
        try {
            // Load the API log model
            $this->CI->load->model('api_log_model');

            // Calculate execution time
            $execution_time = round((microtime(true) - $this->start_time) * 1000, 2); // in milliseconds

            // Prepare response body
            $response_body = '';
            if ($response_data !== null) {
                if (is_array($response_data) || is_object($response_data)) {
                    $response_body = json_encode($response_data);
                } else {
                    $response_body = (string) $response_data;
                }
            }

            // Prepare log data
            $log_data = array(
                'method' => $this->request_data['method'] ?? '',
                'url' => $this->request_data['url'] ?? '',
                'headers' => $this->request_data['headers'] ?? '',
                'request_body' => $this->request_data['request_body'] ?? '',
                'status_code' => $status_code,
                'response_body' => $response_body,
                'ip_address' => $this->request_data['ip_address'] ?? '',
                'execution_time' => $execution_time
            );

            // Save to database
            $result = $this->CI->api_log_model->insert_log($log_data);

            // Log completion
            $log_message = sprintf(
                'API Request Completed: %s %s - Status: %d - Time: %sms - Logged: %s',
                $this->request_data['method'] ?? 'UNKNOWN',
                $this->request_data['url'] ?? 'UNKNOWN',
                $status_code,
                $execution_time,
                $result ? 'YES' : 'NO'
            );

            log_message('info', $log_message);

            return $result;

        } catch (Exception $e) {
            log_message('error', 'Error ending API logging: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Log error specifically
     * 
     * @param string $error_message
     * @param int $status_code
     */
    public function log_error($error_message, $status_code = 500) {
        try {
            $error_response = array(
                'success' => false,
                'message' => $error_message,
                'timestamp' => date('c')
            );

            $this->end_logging($error_response, $status_code);

        } catch (Exception $e) {
            log_message('error', 'Error logging API error: ' . $e->getMessage());
        }
    }

    /**
     * Get full URL including query parameters
     * 
     * @return string
     */
    private function _get_full_url() {
        try {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $uri = $_SERVER['REQUEST_URI'] ?? '/';
            
            return $protocol . '://' . $host . $uri;

        } catch (Exception $e) {
            log_message('error', 'Error getting full URL: ' . $e->getMessage());
            return 'unknown';
        }
    }

    /**
     * Get request headers as JSON string
     * 
     * @return string
     */
    private function _get_request_headers() {
        try {
            $headers = array();
            
            // Get all headers
            if (function_exists('getallheaders')) {
                $headers = getallheaders();
            } else {
                // Fallback for servers that don't support getallheaders()
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        $header_name = str_replace('HTTP_', '', $key);
                        $header_name = str_replace('_', '-', $header_name);
                        $header_name = ucwords(strtolower($header_name), '-');
                        $headers[$header_name] = $value;
                    }
                }
            }

            // Remove sensitive headers
            $sensitive_headers = array('Authorization', 'Cookie', 'X-Api-Key', 'X-Auth-Token');
            foreach ($sensitive_headers as $sensitive) {
                if (isset($headers[$sensitive])) {
                    $headers[$sensitive] = '[REDACTED]';
                }
            }

            return json_encode($headers);

        } catch (Exception $e) {
            log_message('error', 'Error getting request headers: ' . $e->getMessage());
            return '{}';
        }
    }

    /**
     * Get request body
     * 
     * @return string
     */
    private function _get_request_body() {
        try {
            $body = '';

            // Get raw input for POST/PUT/PATCH requests
            if (in_array($this->CI->input->method(), array('post', 'put', 'patch'))) {
                $raw_input = $this->CI->input->raw_input_stream;
                
                if (!empty($raw_input)) {
                    $body = $raw_input;
                } else {
                    // Fallback to POST data
                    $post_data = $this->CI->input->post();
                    if (!empty($post_data)) {
                        $body = json_encode($post_data);
                    }
                }
            }

            // Limit body size to prevent database issues
            if (strlen($body) > 65535) { // 64KB limit
                $body = substr($body, 0, 65535) . '... [TRUNCATED]';
            }

            return $body;

        } catch (Exception $e) {
            log_message('error', 'Error getting request body: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get client IP address
     * 
     * @return string
     */
    private function _get_client_ip() {
        try {
            $ip_keys = array(
                'HTTP_CF_CONNECTING_IP',     // Cloudflare
                'HTTP_CLIENT_IP',            // Proxy
                'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
                'HTTP_X_FORWARDED',          // Proxy
                'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
                'HTTP_FORWARDED_FOR',        // Proxy
                'HTTP_FORWARDED',            // Proxy
                'REMOTE_ADDR'                // Standard
            );

            foreach ($ip_keys as $key) {
                if (array_key_exists($key, $_SERVER) === true) {
                    $ip = $_SERVER[$key];
                    if (strpos($ip, ',') !== false) {
                        $ip = explode(',', $ip)[0];
                    }
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                        return $ip;
                    }
                }
            }

            return $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        } catch (Exception $e) {
            log_message('error', 'Error getting client IP: ' . $e->getMessage());
            return 'unknown';
        }
    }

    /**
     * Get request data for external use
     * 
     * @return array
     */
    public function get_request_data() {
        return $this->request_data;
    }

    /**
     * Get execution time so far
     * 
     * @return float
     */
    public function get_execution_time() {
        return round((microtime(true) - $this->start_time) * 1000, 2);
    }
}
