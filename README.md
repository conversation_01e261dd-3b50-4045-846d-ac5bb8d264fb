# Stockart API - CodeIgniter 3 Version

This is a CodeIgniter 3 implementation of the Stockart Patient Data API, converted from the original JavaScript/Node.js version.

## Overview

The Stockart API is a patient data processing system that:
- Retrieves patient data from a hospital database
- Transforms the data based on different EventTypes
- Sends the transformed data to an external API endpoint

## Features

- **Patient Data Retrieval**: Fetch patient data by `nokun` (visit number) or `ref` (reference number)
- **Event Type Processing**: Support for 4 different event types:
  - `AdmitPatient` - Patient admission
  - `DischargePatient` - Patient discharge
  - `TransferPatient` - Patient transfer (requires `nurseunit` parameter)
  - `UpdatePatient` - Patient information update
- **External API Integration**: Sends processed data to external API endpoint
- **Health Monitoring**: Health check and database connection test endpoints
- **Input Validation**: Comprehensive parameter validation and error handling

## API Endpoints

### Main Endpoints

#### `GET /` or `GET /api`
Returns API information and available endpoints.

**Response:**
```json
{
  "success": true,
  "message": "Stockart API - CodeIgniter 3 Version",
  "version": "1.0.0",
  "server": "localhost",
  "endpoints": {
    "POST /api/v1/stockart/patient": "Process patient data with EventType",
    "GET /api/health": "Health check",
    "GET /api/test-db": "Test database connection"
  }
}
```

#### `POST /api/v1/stockart/patient`
Main patient data processing endpoint.

**Parameters:**
- `nokun` OR `ref` (required) - Patient visit number or reference number (mutually exclusive)
- `eventType` (required) - One of: `AdmitPatient`, `DischargePatient`, `TransferPatient`, `UpdatePatient`
- `nurseunit` (required for TransferPatient) - Nurse unit for patient transfer

**Example Request:**
```bash
curl -X POST -d "nokun=12345&eventType=AdmitPatient" http://localhost:8080/api/v1/stockart/patient
```

**Success Response:**
```json
{
  "success": true,
  "message": "Data berhasil dikirim ke API eksternal dengan EventType: AdmitPatient",
  "data": {
    "patient_data": { ... },
    "api_payload": { ... },
    "api_response": { ... },
    "api_success": true,
    "event_type": "AdmitPatient",
    "additional_params": {},
    "search_type": "nokun",
    "search_param": "12345"
  }
}
```

### Health Check Endpoints

#### `GET /health` or `GET /api/health`
Health check endpoint.

**Response:**
```json
{
  "success": true,
  "message": "API is running",
  "timestamp": "2025-06-04T15:27:30+07:00",
  "version": "1.0.0"
}
```

#### `GET /test-db` or `GET /api/test-db`
Database connection test endpoint.

**Response:**
```json
{
  "success": true,
  "message": "Database connection successful"
}
```

## Configuration

### Database Configuration
Edit `application/config/database.php`:

```php
$db['default'] = array(
    'hostname' => '***********',
    'username' => 'server5',
    'password' => 'simpel',
    'database' => '',  // Will be determined from schema names in queries
    'dbdriver' => 'mysqli',
    'port' => 3306
);
```

### External API Configuration
Edit `application/config/config.php`:

```php
$config['external_api_url'] = 'http://*************:8081/patient';
$config['api_timeout'] = 5000; // milliseconds
```

## Database Schema

The API queries the following database tables:
- `pendaftaran.kunjungan` - Patient visits
- `pendaftaran.pendaftaran` - Patient registrations
- `master.pasien` - Patient master data
- `pendaftaran.tujuan_pasien` - Patient destinations
- `master.dokter` - Doctor master data
- `master.ruang_kamar_tidur` - Room bed data
- `master.ruang_kamar` - Room data

## Installation

1. **Download CodeIgniter 3**: The system files are already included
2. **Configure Database**: Update `application/config/database.php` with your database credentials
3. **Configure External API**: Update `application/config/config.php` with external API settings
4. **Set Permissions**: Ensure `application/logs` and `application/cache` are writable
5. **Start Server**: Use PHP built-in server or configure web server

### Using PHP Built-in Server
```bash
php -S localhost:8080
```

### Using Apache/Nginx
Configure your web server to point to the project root directory.

## Error Handling

The API provides comprehensive error handling:
- **400 Bad Request**: Invalid parameters or validation errors
- **404 Not Found**: Patient data not found or invalid endpoints
- **405 Method Not Allowed**: Invalid HTTP method
- **500 Internal Server Error**: Database or system errors

## Validation Rules

1. **Patient Identifier**: Either `nokun` OR `ref` must be provided (not both)
2. **Event Type**: Must be one of the 4 allowed event types
3. **Transfer Patient**: `nurseunit` parameter is required for `TransferPatient` event type
4. **HTTP Method**: Only POST method is allowed for patient endpoint

## External API Integration

The processed patient data is sent to the configured external API endpoint. The API payload format varies based on the EventType:

### AdmitPatient Payload
```json
{
  "EventType": "AdmitPatient",
  "PatientID": "norm",
  "PatientLastName": "lastName",
  "PatientFirstName": "firstName",
  "NurseUnit": "ICU",
  "EpisodeID": "nokun",
  "BirthDate": "YYYYMMDD",
  "Gender": "M/F",
  "Room": "roomName",
  "Bed": "bedName",
  "AttendingDocID": "doctorId",
  "AttendingDocName": "doctorName",
  "AdmissionDate": "YYYYMMDDHHMM"
}
```

### DischargePatient Payload
```json
{
  "EventType": "DischargePatient",
  "PatientID": "norm",
  "PatientLastName": "lastName",
  "PatientFirstName": "firstName",
  "NurseUnit": "ICU",
  "EpisodeID": "nokun",
  "DischargeDate": "YYYYMMDDHHMM"
}
```

## Timezone

The API uses Asia/Jakarta timezone for all date/time operations.

## Logging

All requests and errors are logged to CodeIgniter's log files in `application/logs/`.

## Version

Current version: 1.0.0

## Conversion Notes

This CodeIgniter 3 version maintains full compatibility with the original JavaScript/Node.js API while providing:
- Better integration with PHP ecosystem
- CodeIgniter's built-in security features
- Structured MVC architecture
- Comprehensive error handling
- Easy configuration management
