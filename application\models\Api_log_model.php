<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_log_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Insert API log entry to database
     * 
     * @param array $log_data
     * @return bool
     */
    public function insert_log($log_data) {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            // Prepare data for insertion
            $data = array(
                'method' => $log_data['method'] ?? '',
                'url' => $log_data['url'] ?? '',
                'headers' => $log_data['headers'] ?? '',
                'request_body' => $log_data['request_body'] ?? '',
                'status_code' => $log_data['status_code'] ?? 200,
                'response_body' => $log_data['response_body'] ?? '',
                'ip_address' => $log_data['ip_address'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            );

            // Insert to log.log_api table
            $result = $this->db->insert('log.log_api', $data);
            
            if (!$result) {
                log_message('error', 'Failed to insert API log: ' . $this->db->error()['message']);
                return false;
            }

            return true;

        } catch (Exception $e) {
            log_message('error', 'Error inserting API log: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get API logs with pagination
     * 
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_logs($limit = 100, $offset = 0) {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            $this->db->select('*');
            $this->db->from('log.log_api');
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit, $offset);

            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                return $query->result_array();
            }

            return array();

        } catch (Exception $e) {
            log_message('error', 'Error getting API logs: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get total count of API logs
     * 
     * @return int
     */
    public function get_logs_count() {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            return $this->db->count_all('log.log_api');

        } catch (Exception $e) {
            log_message('error', 'Error counting API logs: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get API logs by date range
     * 
     * @param string $start_date
     * @param string $end_date
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_logs_by_date($start_date, $end_date, $limit = 100, $offset = 0) {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            $this->db->select('*');
            $this->db->from('log.log_api');
            $this->db->where('created_at >=', $start_date);
            $this->db->where('created_at <=', $end_date);
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit, $offset);

            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                return $query->result_array();
            }

            return array();

        } catch (Exception $e) {
            log_message('error', 'Error getting API logs by date: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get API logs by method
     * 
     * @param string $method
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_logs_by_method($method, $limit = 100, $offset = 0) {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            $this->db->select('*');
            $this->db->from('log.log_api');
            $this->db->where('method', strtoupper($method));
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit, $offset);

            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                return $query->result_array();
            }

            return array();

        } catch (Exception $e) {
            log_message('error', 'Error getting API logs by method: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get API logs by status code
     * 
     * @param int $status_code
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_logs_by_status($status_code, $limit = 100, $offset = 0) {
        try {
            // Load database if not already loaded
            if (!isset($this->db)) {
                $this->load->database();
            }

            $this->db->select('*');
            $this->db->from('log.log_api');
            $this->db->where('status_code', $status_code);
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit, $offset);

            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                return $query->result_array();
            }

            return array();

        } catch (Exception $e) {
            log_message('error', 'Error getting API logs by status: ' . $e->getMessage());
            return array();
        }
    }


}
