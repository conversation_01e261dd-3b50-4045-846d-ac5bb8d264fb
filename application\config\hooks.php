<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	https://codeigniter.com/userguide3/general/hooks.html
|
*/

/*
| -------------------------------------------------------------------------
| API Logging Hooks
| -------------------------------------------------------------------------
| These hooks automatically log all API requests and responses to the database
|
*/

// Hook to start logging before controller execution
$hook['pre_controller'] = array(
    'class'    => '',
    'function' => 'start_api_logging',
    'filename' => 'api_logging.php',
    'filepath' => 'hooks',
    'params'   => array()
);

// Hook to end logging after controller execution
$hook['post_controller'] = array(
    'class'    => '',
    'function' => 'end_api_logging',
    'filename' => 'api_logging.php',
    'filepath' => 'hooks',
    'params'   => array()
);
