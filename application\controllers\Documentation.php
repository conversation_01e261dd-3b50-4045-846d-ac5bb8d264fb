<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Documentation extends CI_Controller {

    public function __construct() {
        parent::__construct();
        // Set timezone to Asia/Jakarta
        date_default_timezone_set('Asia/Jakarta');
    }

    /**
     * Default documentation page
     */
    public function index() {
        $data = array(
            'title' => 'Stockart API Documentation',
            'version' => '1.0.0',
            'server_info' => array(
                'server' => $_SERVER['SERVER_ADDR'] ?? 'localhost',
                'port' => $_SERVER['SERVER_PORT'] ?? '80',
                'protocol' => isset($_SERVER['HTTPS']) ? 'https' : 'http'
            ),
            'base_url' => base_url(),
            'api_endpoints' => array(
                array(
                    'method' => 'GET',
                    'endpoint' => '/api',
                    'description' => 'API Information',
                    'parameters' => array(),
                    'response' => 'JSON with API information'
                ),
                array(
                    'method' => 'POST',
                    'endpoint' => '/api/v1/stockart/patient',
                    'description' => 'Process patient data with EventType',
                    'parameters' => array(
                        array(
                            'name' => 'nokun',
                            'type' => 'string',
                            'required' => 'conditional',
                            'description' => 'Visit number (required if ref is not provided)'
                        ),
                        array(
                            'name' => 'ref',
                            'type' => 'string',
                            'required' => 'conditional',
                            'description' => 'Reference number (required if nokun is not provided)'
                        ),
                        array(
                            'name' => 'eventType',
                            'type' => 'string',
                            'required' => 'yes',
                            'description' => 'Event type: AdmitPatient, DischargePatient, TransferPatient, UpdatePatient'
                        ),
                        array(
                            'name' => 'nurseunit',
                            'type' => 'string',
                            'required' => 'conditional',
                            'description' => 'Nurse unit (required for TransferPatient event type)'
                        )
                    ),
                    'response' => 'JSON with processed patient data'
                ),
                array(
                    'method' => 'GET',
                    'endpoint' => '/api/health',
                    'description' => 'Health check endpoint',
                    'parameters' => array(),
                    'response' => 'JSON with API health status'
                ),
                array(
                    'method' => 'GET',
                    'endpoint' => '/api/test-db',
                    'description' => 'Test database connection',
                    'parameters' => array(),
                    'response' => 'JSON with database connection status'
                )
            )
        );

        $this->load->view('documentation/index', $data);
    }
}
