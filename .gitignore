# CodeIgniter 3 .gitignore

# Application cache and logs
application/cache/*
!application/cache/index.html
!application/cache/.htaccess

application/logs/*
!application/logs/index.html
!application/logs/.htaccess

# User uploads
application/uploads/*
!application/uploads/index.html

# Environment files
.env
.env.*

# IDE files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# Composer
vendor/
composer.lock

# Node.js (if using for frontend assets)
node_modules/
npm-debug.log
yarn-error.log

# Original JavaScript files (keeping for reference)
# Uncomment if you want to exclude them
# server.js
# StockartController.js
# Stockart.js

# Backup files
*.bak
*.backup

# Database dumps
*.sql
*.sqlite

# Configuration files with sensitive data (uncomment if needed)
# application/config/database.php
# application/config/config.php
