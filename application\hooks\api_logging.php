<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * API Logging Hook
 * 
 * This hook automatically logs all API requests and responses
 */
class Api_logging_hook {

    private $CI;
    private $api_logger;

    public function __construct() {
        $this->CI =& get_instance();
    }

    /**
     * Pre-controller hook - start logging
     */
    public function start_logging() {
        try {
            // Get CI instance
            $this->CI =& get_instance();

            // Check if CI is loaded properly
            if (!$this->CI) {
                return;
            }

            // Load the API logger library
            $this->CI->load->library('api_logger');
            $this->api_logger = $this->CI->api_logger;

            // Start logging
            $this->api_logger->start_logging();

            // Store logger instance in CI for later use
            $this->CI->_api_logger_instance = $this->api_logger;

        } catch (Exception $e) {
            if (function_exists('log_message')) {
                log_message('error', 'Error in API logging hook start: ' . $e->getMessage());
            }
        }
    }

    /**
     * Post-controller hook - end logging
     */
    public function end_logging() {
        try {
            // Get CI instance
            $this->CI =& get_instance();

            // Check if CI is loaded properly
            if (!$this->CI) {
                return;
            }

            // Get the logger instance
            if (isset($this->CI->_api_logger_instance)) {
                $api_logger = $this->CI->_api_logger_instance;

                // Get response data from output
                $response_data = $this->CI->output->get_output();
                $status_code = $this->_get_status_code();

                // End logging
                $api_logger->end_logging($response_data, $status_code);
            }

        } catch (Exception $e) {
            if (function_exists('log_message')) {
                log_message('error', 'Error in API logging hook end: ' . $e->getMessage());
            }
        }
    }

    /**
     * Get HTTP status code from headers
     * 
     * @return int
     */
    private function _get_status_code() {
        try {
            // Check if status header was set
            $headers = $this->CI->output->get_header();
            
            // Look for status header
            foreach ($headers as $header) {
                if (strpos($header['name'], 'Status') !== false || 
                    strpos($header['name'], 'HTTP/') !== false) {
                    
                    // Extract status code from header
                    if (preg_match('/(\d{3})/', $header['value'], $matches)) {
                        return (int) $matches[1];
                    }
                }
            }

            // Default to 200 if no status header found
            return 200;

        } catch (Exception $e) {
            log_message('error', 'Error getting status code: ' . $e->getMessage());
            return 200;
        }
    }
}

/**
 * Hook functions (required by CodeIgniter)
 */

function start_api_logging() {
    try {
        $hook = new Api_logging_hook();
        $hook->start_logging();
    } catch (Exception $e) {
        // Silently fail to prevent breaking the application
        if (function_exists('log_message')) {
            log_message('error', 'Error in start_api_logging function: ' . $e->getMessage());
        }
    }
}

function end_api_logging() {
    try {
        $hook = new Api_logging_hook();
        $hook->end_logging();
    } catch (Exception $e) {
        // Silently fail to prevent breaking the application
        if (function_exists('log_message')) {
            log_message('error', 'Error in end_api_logging function: ' . $e->getMessage());
        }
    }
}
