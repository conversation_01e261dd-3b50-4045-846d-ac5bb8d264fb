<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class External_api {

    protected $CI;
    protected $api_url;
    protected $timeout;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->config('config');
        
        $this->api_url = $this->CI->config->item('external_api_url') ?: 'http://*************:8081/patient';
        $this->timeout = $this->CI->config->item('api_timeout') ?: 5000;
    }

    /**
     * Send data to external API
     * 
     * @param array $data
     * @param string $api_url (optional)
     * @return array
     */
    public function send_to_external_api($data, $api_url = null) {
        $url = $api_url ?: $this->api_url;
        $timeout = $this->timeout / 1000; // Convert milliseconds to seconds

        // Prepare the data for sending
        $json_data = json_encode($data);
        
        // Initialize cURL
        $ch = curl_init();
        
        // Set cURL options
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $json_data,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($json_data)
            ),
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => $timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ));

        // Execute the request
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        
        curl_close($ch);

        // Handle cURL errors
        if ($response === false || !empty($curl_error)) {
            if (strpos($curl_error, 'timeout') !== false || strpos($curl_error, 'timed out') !== false) {
                return array(
                    'success' => false,
                    'message' => 'Timeout saat menghubungi API eksternal',
                    'error' => 'Request timeout',
                    'status_code' => 0
                );
            }
            
            return array(
                'success' => false,
                'message' => 'Koneksi ke API eksternal gagal',
                'error' => 'Connection failed: ' . $curl_error,
                'status_code' => 0
            );
        }

        // Decode the response
        $decoded_response = json_decode($response, true);
        
        // Check if response is valid JSON
        if (json_last_error() !== JSON_ERROR_NONE) {
            $decoded_response = $response; // Use raw response if not JSON
        }

        // Determine success based on HTTP status code
        $is_success = ($http_code >= 200 && $http_code < 300);

        if ($is_success) {
            return array(
                'success' => true,
                'message' => 'Data berhasil dikirim ke API eksternal',
                'status_code' => $http_code,
                'response' => $decoded_response
            );
        } else {
            return array(
                'success' => false,
                'message' => 'API eksternal mengembalikan error',
                'status_code' => $http_code,
                'error' => $decoded_response
            );
        }
    }

    /**
     * Test external API connection
     * 
     * @return array
     */
    public function test_connection() {
        $test_data = array(
            'test' => true,
            'timestamp' => date('Y-m-d H:i:s')
        );

        return $this->send_to_external_api($test_data);
    }

    /**
     * Set API URL
     * 
     * @param string $url
     */
    public function set_api_url($url) {
        $this->api_url = $url;
    }

    /**
     * Set timeout
     * 
     * @param int $timeout_ms Timeout in milliseconds
     */
    public function set_timeout($timeout_ms) {
        $this->timeout = $timeout_ms;
    }

    /**
     * Get current API URL
     * 
     * @return string
     */
    public function get_api_url() {
        return $this->api_url;
    }

    /**
     * Get current timeout
     * 
     * @return int Timeout in milliseconds
     */
    public function get_timeout() {
        return $this->timeout;
    }
}
