# Memory Bank - Stockart API CodeIgniter 3 Conversion

## Project Information
- **Project Name**: Stockart Patient Data API
- **Framework**: CodeIgniter 3.1.13
- **Original**: JavaScript/Node.js with Express
- **Conversion Date**: June 2025
- **Status**: Successfully Completed
- **Repository**: https://gitlab.com/rizqibennington/simrs-api.git
- **Git Status**: Successfully pushed to GitLab
- **Git Author**: riz<PERSON><PERSON><PERSON> (<EMAIL>)

## Database Configuration
- **Host**: ***********
- **Port**: 3306
- **Username**: server5
- **Password**: simpel
- **Database**: Not specified (uses schema names in queries)
- **Schemas Used**: pendaftaran, master

## Key Architecture Decisions

### MVC Structure
- **Model**: Stockart_model.php - Handles all database operations and data processing
- **View**: Error views only (API returns JSON)
- **Controller**: Stockart.php - Handles all API endpoints and request processing
- **Library**: External_api.php - Handles external API communication

### Database Connection Strategy
- Implemented lazy loading to handle database connection issues gracefully
- No specific database name configured - relies on schema names in queries
- Connection testing available via /test-db endpoint

### Error Handling Strategy
- Comprehensive validation at controller level
- Database errors caught and logged
- External API errors handled with fallback responses
- Proper HTTP status codes for all error conditions

## API Endpoints Mapping

### Original JavaScript → CodeIgniter 3
1. `GET /` → `Stockart::index()`
2. `GET /api` → `Stockart::index()`
3. `POST /api/v1/stockart/patient` → `Stockart::patient()`
4. `GET /health` → `Stockart::health_check()`
5. `GET /api/health` → `Stockart::health_check()`
6. `GET /api/logs` → `Stockart::api_logs()`

## EventType Processing

### Supported EventTypes
1. **AdmitPatient**: Full patient data with admission details
2. **DischargePatient**: Patient data with discharge date
3. **TransferPatient**: Patient data with nurse unit (requires nurseunit parameter)
4. **UpdatePatient**: Basic patient data update

### Data Transformation Logic
- Patient name split into first/last name
- Gender conversion (1=M, 2=F)
- Date formatting to YYYYMMDD and YYYYMMDDHHMM
- Dynamic payload based on EventType

## External API Integration
- **Endpoint**: http://*************:8081/patient
- **Method**: POST
- **Timeout**: 5000ms
- **Content-Type**: application/json
- **Error Handling**: Comprehensive with fallback responses

## File Structure
```
ci3-api/
├── index.php
├── .htaccess
├── README.md
├── Todo.md
├── Memory_Bank.md
├── application/
│   ├── config/
│   │   ├── autoload.php
│   │   ├── config.php
│   │   ├── database.php
│   │   └── routes.php
│   ├── controllers/
│   │   └── Stockart.php
│   ├── models/
│   │   └── Stockart_model.php
│   ├── libraries/
│   │   └── External_api.php
│   ├── views/
│   │   └── errors/html/
│   │       ├── error_404.php
│   │       ├── error_general.php
│   │       ├── error_php.php
│   │       └── error_db.php
│   └── [other CI directories]
└── system/ [CodeIgniter 3.1.13 system files]
```

## Key Code Patterns

### Database Query Pattern
```php
// Load database if not already loaded
if (!isset($this->db)) {
    $this->load->database();
}

$query = "SELECT ... FROM schema.table WHERE condition = ?";
$result = $this->db->query($query, array($parameter));
return $result->num_rows() > 0 ? $result->row_array() : null;
```

### Error Response Pattern
```php
private function _send_error_response($message, $status_code = 400) {
    $this->output->set_status_header($status_code);
    $response = array(
        'success' => false,
        'message' => $message
    );
    $this->output->set_output(json_encode($response));
}
```

### External API Call Pattern
```php
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => array('Content-Type: application/json'),
    CURLOPT_TIMEOUT => $timeout
));
$response = curl_exec($ch);
```

## Testing Results
- ✅ API Information endpoint working
- ✅ Health check endpoint working
- ✅ Database connection test working
- ✅ Parameter validation working
- ✅ EventType validation working
- ✅ Error handling working
- ✅ JSON responses properly formatted

## Configuration Notes

### Timezone
- Set to Asia/Jakarta in both config and controller
- Affects all date/time operations

### CORS Configuration
- Configured in .htaccess for cross-origin requests
- Allows all origins (*) for development

### URL Rewriting
- .htaccess removes index.php from URLs
- Handles OPTIONS requests for CORS preflight

## Security Considerations
- SQL injection protection via parameterized queries
- XSS protection via JSON-only responses
- Input validation on all parameters
- Error message sanitization in production

## Performance Optimizations
- Lazy database loading
- Efficient query structure
- Minimal autoloading
- Direct JSON output without views

## Troubleshooting Guide

### Database Connection Issues
1. Check database credentials in config/database.php
2. Verify network connectivity to ***********:3306
3. Test connection via /test-db endpoint
4. Check error logs in application/logs/

### External API Issues
1. Verify external API URL in config/config.php
2. Check network connectivity to *************:8081
3. Review timeout settings
4. Check API response in logs

### Common Error Codes
- 400: Parameter validation failed
- 404: Patient data not found or invalid endpoint
- 405: Invalid HTTP method
- 500: Database or system error

## API Logging System (Added June 2025) ✅ COMPLETED

### Database Logging
- **Table**: log.log_api
- **Columns**: id, method, url, headers, request_body, status_code, response_body, ip_address, created_at
- **Auto-logging**: All API requests automatically logged via controller integration
- **Status**: Fully operational and tested

### Logging Components
- **Model**: Api_log_model.php - Database operations for logging
- **Library**: Api_logger.php - Core logging functionality
- **Hook**: api_logging.php - Automatic request/response capture (backup method)
- **Controller Integration**: Direct logging in all controller methods
- **Endpoints**: /api/logs

### Logging Features
- ✅ Automatic request/response capture
- ✅ IP address detection (supports proxies/load balancers)
- ✅ Header sanitization (removes sensitive data)
- ✅ Request body size limiting (64KB max)
- ✅ Execution time tracking
- ✅ Status code capture
- ✅ Comprehensive error logging
- ✅ Log filtering by method, status code, date range
- ✅ Pagination support for large log datasets
- ✅ Real-time logging verification

### Production Features
- ✅ Successfully logs GET requests
- ✅ Successfully logs POST requests with JSON body
- ✅ Correctly captures HTTP status codes (200, 404, etc.)
- ✅ Properly records request headers and sanitizes sensitive data
- ✅ Accurately captures client IP addresses
- ✅ Log filtering works correctly
- ✅ All endpoints return proper JSON responses

## Future Enhancement Ideas
- API authentication/authorization
- Request rate limiting
- Response caching
- API versioning
- Swagger documentation
- Unit testing framework
- Performance monitoring
- Database connection pooling
- Log retention policies
- Log analytics dashboard

## Lessons Learned
1. Lazy loading prevents startup failures
2. Comprehensive validation improves API reliability
3. Proper error handling enhances user experience
4. Documentation is crucial for maintenance
5. Testing each endpoint individually ensures quality
