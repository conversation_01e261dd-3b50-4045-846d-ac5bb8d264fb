<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .server-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .info-card strong {
            color: #667eea;
        }

        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8em;
            margin-right: 10px;
        }

        .method.get {
            background: #28a745;
            color: white;
        }

        .method.post {
            background: #007bff;
            color: white;
        }

        .endpoint-path {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }

        .endpoint-body {
            padding: 15px;
        }

        .parameters-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .parameters-table th,
        .parameters-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .parameters-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .required {
            color: #dc3545;
            font-weight: bold;
        }

        .optional {
            color: #6c757d;
        }

        .conditional {
            color: #fd7e14;
            font-weight: bold;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin-top: 10px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo $title; ?></h1>
            <p>RESTful API for Patient Data Management</p>
            <div class="version-badge">Version <?php echo $version; ?></div>
        </div>

        <div class="section">
            <h2>📋 Server Information</h2>
            <div class="server-info">
                <div class="info-card">
                    <strong>Server:</strong> <?php echo $server_info['server']; ?>
                </div>
                <div class="info-card">
                    <strong>Port:</strong> <?php echo $server_info['port']; ?>
                </div>
                <div class="info-card">
                    <strong>Protocol:</strong> <?php echo $server_info['protocol']; ?>
                </div>
                <div class="info-card">
                    <strong>Base URL:</strong> <?php echo $base_url; ?>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 API Endpoints</h2>
            
            <?php foreach ($api_endpoints as $endpoint): ?>
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method <?php echo strtolower($endpoint['method']); ?>"><?php echo $endpoint['method']; ?></span>
                    <span class="endpoint-path"><?php echo $endpoint['endpoint']; ?></span>
                </div>
                <div class="endpoint-body">
                    <p><strong>Description:</strong> <?php echo $endpoint['description']; ?></p>
                    
                    <?php if (!empty($endpoint['parameters'])): ?>
                    <h4>Parameters:</h4>
                    <table class="parameters-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($endpoint['parameters'] as $param): ?>
                            <tr>
                                <td><code><?php echo $param['name']; ?></code></td>
                                <td><?php echo $param['type']; ?></td>
                                <td>
                                    <span class="<?php echo $param['required']; ?>">
                                        <?php echo ucfirst($param['required']); ?>
                                    </span>
                                </td>
                                <td><?php echo $param['description']; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php endif; ?>
                    
                    <p><strong>Response:</strong> <?php echo $endpoint['response']; ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="section">
            <h2>📝 Usage Examples</h2>

            <h3>1. Get API Information</h3>
            <div class="code-block">
curl -X GET "<?php echo $base_url; ?>api" \
  -H "Accept: application/json"
            </div>

            <h3>2. Process Patient Data - Admit Patient</h3>
            <div class="code-block">
curl -X POST "<?php echo $base_url; ?>api/v1/stockart/patient" \
  -H "Content-Type: application/json" \
  -d '{
    "nokun": "123456789",
    "eventType": "AdmitPatient"
  }'
            </div>

            <h3>3. Process Patient Data - Transfer Patient</h3>
            <div class="code-block">
curl -X POST "<?php echo $base_url; ?>api/v1/stockart/patient" \
  -H "Content-Type: application/json" \
  -d '{
    "ref": "REF123456",
    "eventType": "TransferPatient",
    "nurseunit": "ICU"
  }'
            </div>

            <h3>4. Health Check</h3>
            <div class="code-block">
curl -X GET "<?php echo $base_url; ?>api/health" \
  -H "Accept: application/json"
            </div>
        </div>

        <div class="section">
            <h2>📊 Response Format</h2>

            <h3>Success Response</h3>
            <div class="code-block">
{
  "success": true,
  "message": "Data berhasil dikirim ke API eksternal dengan EventType: AdmitPatient",
  "data": {
    "patient_data": {
      "nokun": "123456789",
      "nama_pasien": "John Doe",
      "norm": "MR123456",
      "jenis_kelamin": 1,
      "tanggal_lahir": "1990-01-01",
      "id_dokter": 1,
      "nama_dokter": "Dr. Smith",
      "kasur": "A01",
      "kamar": "101",
      "tanggal_kunjungan": "2024-01-15 10:30:00",
      "ref": "REF123456"
    },
    "api_payload": {
      "EventType": "AdmitPatient",
      "PatientID": "MR123456",
      "PatientLastName": "Doe",
      "PatientFirstName": "John",
      "NurseUnit": "ICU",
      "EpisodeID": "123456789",
      "BirthDate": "19900101",
      "Gender": "M",
      "Room": "101",
      "Bed": "A01",
      "AttendingDocID": "1",
      "AttendingDocName": "Dr. Smith",
      "AdmissionDate": "202401151030"
    },
    "api_response": {
      "success": true,
      "message": "Data berhasil dikirim ke API eksternal",
      "status_code": 200
    },
    "api_success": true,
    "event_type": "AdmitPatient",
    "search_type": "nokun",
    "search_param": "123456789"
  }
}
            </div>

            <h3>Error Response</h3>
            <div class="code-block">
{
  "success": false,
  "message": "Parameter nokun atau ref wajib diisi (pilih salah satu)"
}
            </div>
        </div>

        <div class="section">
            <h2>⚙️ Event Types</h2>

            <table class="parameters-table">
                <thead>
                    <tr>
                        <th>Event Type</th>
                        <th>Description</th>
                        <th>Required Fields</th>
                        <th>Additional Parameters</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>AdmitPatient</code></td>
                        <td>Register new patient admission</td>
                        <td>nokun/ref, eventType</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td><code>DischargePatient</code></td>
                        <td>Process patient discharge</td>
                        <td>nokun/ref, eventType</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td><code>TransferPatient</code></td>
                        <td>Transfer patient to different unit</td>
                        <td>nokun/ref, eventType, nurseunit</td>
                        <td>nurseunit (required)</td>
                    </tr>
                    <tr>
                        <td><code>UpdatePatient</code></td>
                        <td>Update patient information</td>
                        <td>nokun/ref, eventType</td>
                        <td>None</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔒 Authentication & Security</h2>
            <p>Currently, this API does not require authentication. However, it's recommended to implement proper authentication and authorization mechanisms for production use.</p>

            <h3>Security Features:</h3>
            <ul style="margin-left: 20px; margin-top: 10px;">
                <li>Input validation and sanitization</li>
                <li>SQL injection prevention through Query Builder</li>
                <li>Error logging and monitoring</li>
                <li>Request method validation</li>
            </ul>
        </div>

        <div class="section">
            <h2>📞 Support & Contact</h2>
            <p>For technical support or questions about this API, please contact the development team.</p>

            <div class="server-info" style="margin-top: 15px;">
                <div class="info-card">
                    <strong>Framework:</strong> CodeIgniter 3
                </div>
                <div class="info-card">
                    <strong>PHP Version:</strong> <?php echo phpversion(); ?>
                </div>
                <div class="info-card">
                    <strong>Environment:</strong> <?php echo ENVIRONMENT; ?>
                </div>
                <div class="info-card">
                    <strong>Last Updated:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>&copy; <?php echo date('Y'); ?> Stockart API Documentation. Built with CodeIgniter 3.</p>
    </div>
</body>
</html>
